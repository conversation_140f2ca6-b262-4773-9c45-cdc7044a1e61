'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { TeamLineup, MatchEvents, Player } from '@/types/fixture';
import { Fixture } from '@/lib/api';

interface FootballPitchProps {
  homeLineup: TeamLineup;
  awayLineup: TeamLineup;
  fixture: Fixture;
  events?: MatchEvents;
}

export default function FootballPitch({
  homeLineup,
  awayLineup,
  fixture,
  events
}: FootballPitchProps) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Get player goals from events
  const getPlayerGoals = (playerId: number, teamId: number): number => {
    if (!events) return 0;
    
    return events.filter(event => 
      event.player?.id === playerId && 
      event.team.id === teamId && 
      event.type === 'Goal' &&
      !event.detail.toLowerCase().includes('own')
    ).length;
  };

  // Get player cards from events
  const getPlayerCards = (playerId: number, teamId: number): { yellow: number; red: number } => {
    if (!events) return { yellow: 0, red: 0 };
    
    const cardEvents = events.filter(event => 
      event.player?.id === playerId && 
      event.team.id === teamId && 
      event.type === 'Card'
    );
    
    const yellow = cardEvents.filter(event => 
      event.detail.toLowerCase().includes('yellow')
    ).length;
    
    const red = cardEvents.filter(event => 
      event.detail.toLowerCase().includes('red')
    ).length;
    
    return { yellow, red };
  };

  const convertGridToPosition = (
    apiGrid: string,
    isHome: boolean,
    lineup: { player: Player }[],
    isMobile: boolean = false
  ) => {
    const [apiColumn, apiRow] = apiGrid.split(':').map(Number);

    const totalPlayersInLine = lineup.filter(p =>
      p.player.grid?.startsWith(`${apiColumn}:`)
    ).length;

    if (isMobile) {
      // Vertical layout for mobile
      const baseXPositionPercentage = (apiRow - 0.5) / totalPlayersInLine * 100;
      const xPositionPercentage = isHome ? baseXPositionPercentage : (100 - baseXPositionPercentage);

      const homeColumnPositions = {
        1: 95, 2: 78, 3: 62, 4: 46, 5: 30
      };
      const awayColumnPositions = {
        1: 5, 2: 22, 3: 38, 4: 54, 5: 70
      };
      const columnPositions = isHome ? homeColumnPositions : awayColumnPositions;
      const yPositionPercentage = columnPositions[apiColumn as keyof typeof columnPositions] || 50;

      return { x: xPositionPercentage, y: yPositionPercentage };
    } else {
      // Horizontal layout for desktop
      const yPositionPercentage = (apiRow - 0.5) / totalPlayersInLine * 100;

      const homeColumnPositions = {
        1: 5, 2: 22, 3: 38, 4: 54, 5: 70
      };
      const awayColumnPositions = {
        1: 95, 2: 78, 3: 62, 4: 46, 5: 30
      };
      const columnPositions = isHome ? homeColumnPositions : awayColumnPositions;
      const xPositionPercentage = columnPositions[apiColumn as keyof typeof columnPositions] || 50;

      return { x: xPositionPercentage, y: yPositionPercentage };
    }
  };

  // Render a player
  const renderPlayer = (player: Player, isHome: boolean) => {
    const goals = getPlayerGoals(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);
    const cards = getPlayerCards(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);

    const homeColors = homeLineup.team.colors?.player;
    const awayColors = awayLineup.team.colors?.player;
    const homeGkColors = homeLineup.team.colors?.goalkeeper;
    const awayGkColors = awayLineup.team.colors?.goalkeeper;

    let playerColor: string;
    let textColor: string;
    
    if (player.pos === 'G') {
      playerColor = isHome ? `#${homeGkColors?.primary || '059669'}` : `#${awayGkColors?.primary || 'dc2626'}`;
      textColor = isHome ? `#${homeGkColors?.number || 'ffffff'}` : `#${awayGkColors?.number || 'ffffff'}`;
    } else {
      playerColor = isHome ? `#${homeColors?.primary || '3b82f6'}` : `#${awayColors?.primary || 'ef4444'}`;
      textColor = isHome ? `#${homeColors?.number || 'ffffff'}` : `#${awayColors?.number || 'ffffff'}`;
    }

    return (
      <div key={player.id} className="flex flex-col items-center">
        {/* Player Jersey Container */}
        <div className="relative w-10 h-10">
          {/* SVG Jersey Shape */}
          <svg
            viewBox="0 0 36 36"
            className="w-full h-full drop-shadow-lg"
          >
            <path
              fill={playerColor}
              stroke="#FFFFFF"
              strokeWidth="1.5"
              d="M 6 5 L 12 5 C 14 3, 22 3, 24 5 L 30 5 L 34 10 L 30 14 L 30 31 L 6 31 L 6 14 L 2 10 L 6 5 Z"
            />
          </svg>

          {/* Player Number */}
          <div
            className="absolute inset-0 flex items-center justify-center font-bold text-sm"
            style={{ color: textColor }}
          >
            <span>{player.number || '?'}</span>
          </div>

          {/* Goal indicator */}
          {goals > 0 && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center text-xs text-white font-bold">
              {goals}
            </div>
          )}

          {/* Card indicators (Prioritizes Red over Yellow) */}
          {cards.red > 0 ? (
            <div className="absolute -top-1.5 -left-1.5 w-3 h-4 bg-red-500 rounded-sm"></div>
          ) : cards.yellow > 0 && (
            <div className="absolute -top-1.5 -left-1.5 w-3 h-4 bg-yellow-400 rounded-sm"></div>
          )}
        </div>

        {/* Player Name */}
        <div 
          className="mt-1 text-xs text-center text-white font-medium max-w-16 truncate"
          style={{ textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)' }}
        >
          {player.name.split(' ').pop()}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-card rounded-lg border border-border p-4 md:p-6">
      {/* Team Headers */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-3">
          <Image
            src={homeLineup.team.logo}
            alt={homeLineup.team.name}
            width={32}
            height={32}
            className="w-8 h-8"
          />
          <div>
            <h4 className="font-semibold">{homeLineup.team.name}</h4>
            {homeLineup.formation && (
              <p className="text-sm text-muted-foreground">{homeLineup.formation}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div>
            <h4 className="font-semibold text-right">{awayLineup.team.name}</h4>
            {awayLineup.formation && (
              <p className="text-sm text-muted-foreground text-right">{awayLineup.formation}</p>
            )}
          </div>
          <Image
            src={awayLineup.team.logo}
            alt={awayLineup.team.name}
            width={32}
            height={32}
            className="w-8 h-8"
          />
        </div>
      </div>

      {/* Football Pitch */}
      <div className="w-full max-w-6xl mx-auto">
        <div
          className="relative w-full shadow-lg overflow-hidden"
          style={{
            paddingBottom: isMobile ? '150%' : '60%',
            backgroundColor: 'var(--pitch-color, #17AA61)'
          }}
        >
          {/* Pitch Markings */}
          <svg className="absolute inset-0 w-full h-full">
            {/* Outer boundary */}
            <rect x="2" y="2" width="calc(100% - 4px)" height="calc(100% - 4px)" fill="none" stroke="white" strokeWidth="2"/>

            {/* Center line and circle - responsive */}
            <g className="hidden md:block">
              {/* Horizontal layout for desktop */}
              <line x1="50%" y1="0" x2="50%" y2="100%" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="50%" r="12%" fill="none" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="50%" r="0.5%" fill="white"/>
              <rect x="0" y="35%" width="8%" height="30%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="92%" y="35%" width="8%" height="30%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="0" y="25%" width="15%" height="50%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="85%" y="25%" width="15%" height="50%" fill="none" stroke="white" strokeWidth="2"/>
              <circle cx="10%" cy="50%" r="0.5%" fill="white"/>
              <circle cx="90%" cy="50%" r="0.5%" fill="white"/>
            </g>

            <g className="block md:hidden">
              {/* Vertical layout for mobile */}
              <line x1="0" y1="50%" x2="100%" y2="50%" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="50%" r="8%" fill="none" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="50%" r="0.5%" fill="white"/>
              <rect x="35%" y="0" width="30%" height="8%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="35%" y="92%" width="30%" height="8%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="25%" y="0" width="50%" height="15%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="25%" y="85%" width="50%" height="15%" fill="none" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="10%" r="0.5%" fill="white"/>
              <circle cx="50%" cy="90%" r="0.5%" fill="white"/>
            </g>
          </svg>

          {/* Players Positioned Using Grid System */}
          <div className="absolute inset-0">
            {homeLineup.startXI?.map((playerData) => {
              const player = playerData.player;
              if (!player.grid || !homeLineup.startXI) return null;
              const position = convertGridToPosition(player.grid, true, homeLineup.startXI, isMobile);
              return (
                <div
                  key={player.id}
                  className="absolute"
                  style={{
                    left: `${position.x}%`,
                    top: `${position.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  {renderPlayer(player, true)}
                </div>
              );
            })}

            {awayLineup.startXI?.map((playerData) => {
              const player = playerData.player;
              if (!player.grid || !awayLineup.startXI) return null;
              const position = convertGridToPosition(player.grid, false, awayLineup.startXI, isMobile);
              return (
                <div
                  key={player.id}
                  className="absolute"
                  style={{
                    left: `${position.x}%`,
                    top: `${position.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  {renderPlayer(player, false)}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}