'use client';

import MatchStatistics from './MatchStatistics';
import TeamForm from './TeamForm';
import MatchEvents from './MatchEvents';
import MatchLineups from './MatchLineups';
import HeadToHead from './HeadToHead';
import { FixturePageData } from '@/types/fixture';

interface FixtureContentProps {
  activeTab: string;
  fixtureData: FixturePageData;
}

export default function FixtureContent({ activeTab, fixtureData }: FixtureContentProps) {
  const { fixture, statistics, events, lineups, headToHead, predictions } = fixtureData;
  const isUpcoming = fixture.fixture.status.short === 'NS';

  const renderContent = () => {
    switch (activeTab) {
      case 'facts':
      case 'preview':
        return (
          <div className="bg-card rounded-lg border border-border p-4 md:p-6">
            {isUpcoming ? (
              <div>
                <h3 className="text-lg font-semibold mb-6">Match Preview</h3>
                
                {/* Team Form */}
                <div className="mb-6">
                  <h4 className="font-medium mb-3">Team Form</h4>
                  <TeamForm fixture={fixture} />
                </div>

                {/* About the Match */}
                <div className="mb-6">
                  <h4 className="font-medium mb-3">About the Match</h4>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <p>
                      {fixture.teams.home.name} is playing {fixture.teams.away.name} at {fixture.fixture.venue.name || 'TBD'} 
                      {fixture.fixture.venue.city && ` in ${fixture.fixture.venue.city}`} on {new Date(fixture.fixture.date).toLocaleDateString()}.
                    </p>
                    {fixture.league.round && (
                      <p>This is a {fixture.league.round} match in the {fixture.league.name}.</p>
                    )}
                  </div>
                </div>

                {/* Predictions */}
                {predictions && (
                  <div>
                    <h4 className="font-medium mb-3">Predictions</h4>
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="text-sm text-muted-foreground mb-1">Home Win</div>
                        <div className="font-semibold text-foreground">{predictions.predictions?.percent?.home || 'N/A'}</div>
                      </div>
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="text-sm text-muted-foreground mb-1">Draw</div>
                        <div className="font-semibold text-foreground">{predictions.predictions?.percent?.draw || 'N/A'}</div>
                      </div>
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="text-sm text-muted-foreground mb-1">Away Win</div>
                        <div className="font-semibold text-foreground">{predictions.predictions?.percent?.away || 'N/A'}</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div>
                <h3 className="text-lg font-semibold mb-6">Match Facts</h3>
                
                {/* Key Events Summary */}
                {events && events.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-medium mb-3">Key Events</h4>
                    <div className="space-y-2">
                      {events.slice(0, 5).map((event, index: number) => (
                        <div key={index} className="flex items-center space-x-3 text-sm">
                          <span className="font-mono text-muted-foreground w-8">
                            {event.time.elapsed}&apos;
                          </span>
                          <span className="font-medium">{event.type}</span>
                          <span>{event.player?.name}</span>
                          <span className="text-muted-foreground">({event.team?.name})</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Match Info */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Referee:</span>
                    <span className="ml-2">{fixture.fixture.referee || 'TBD'}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Venue:</span>
                    <span className="ml-2">{fixture.fixture.venue.name || 'TBD'}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Competition:</span>
                    <span className="ml-2">{fixture.league.name}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Round:</span>
                    <span className="ml-2">{fixture.league.round}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'commentary':
        return <MatchEvents events={events} fixture={fixture} />;

      case 'stats':
        return <MatchStatistics statistics={statistics} fixture={fixture} lineups={lineups} />;

      case 'lineup':
        return <MatchLineups lineups={lineups} fixture={fixture} events={events} statistics={statistics} />;

      case 'h2h':
        return <HeadToHead headToHead={headToHead} fixture={fixture} />;

      default:
        return (
          <div className="bg-card rounded-lg border border-border p-4 md:p-6">
            <p className="text-muted-foreground">Content not available</p>
          </div>
        );
    }
  };

  return <div>{renderContent()}</div>;
}
