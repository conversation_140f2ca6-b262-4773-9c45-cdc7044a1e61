'use client';


import { Fixture } from '@/lib/api';
import type { MatchLineups, MatchEvents, MatchStatistics } from '@/types/fixture';
import FootballPitch from './FootballPitch';

interface MatchLineupsProps {
  lineups?: MatchLineups;
  fixture: Fixture;
  events?: MatchEvents;
  statistics?: MatchStatistics;
}

export default function MatchLineups({ lineups, fixture, events, statistics }: MatchLineupsProps) {
  if (!lineups || lineups.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold mb-4">Lineups</h3>
        <p className="text-muted-foreground">Lineups not available for this match.</p>
      </div>
    );
  }

  const homeLineup = lineups.find(lineup => lineup.team.id === fixture.teams.home.id);
  const awayLineup = lineups.find(lineup => lineup.team.id === fixture.teams.away.id);

  if (!homeLineup || !awayLineup) {
    return (
      <div className="bg-card rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold mb-4">Lineups</h3>
        <p className="text-muted-foreground">Lineup data incomplete.</p>
      </div>
    );
  }

  return (
    <FootballPitch
      homeLineup={homeLineup}
      awayLineup={awayLineup}
      fixture={fixture}
      events={events}
      statistics={statistics}
    />
  );
}
