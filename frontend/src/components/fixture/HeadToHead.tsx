'use client';

import { Fixture } from '@/lib/api';
import { HeadToHeadData, HeadToHeadMatch } from '@/types/fixture';

interface HeadToHeadProps {
  headToHead?: HeadToHeadData;
  fixture: Fixture;
}

export default function HeadToHead({ headToHead, fixture }: HeadToHeadProps) {
  if (!headToHead || headToHead.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold mb-4">Head-to-Head</h3>
        <p className="text-muted-foreground">No head-to-head data available.</p>
      </div>
    );
  }

  // Helper function to determine match result for a team
  const getMatchResult = (match: HeadToHeadMatch, teamId: number): 'W' | 'D' | 'L' => {
    if (match.goals.home === null || match.goals.away === null) return 'D';
    
    const isHome = match.teams.home.id === teamId;
    const homeGoals = match.goals.home;
    const awayGoals = match.goals.away;
    
    if (homeGoals === awayGoals) return 'D';
    
    if (isHome) {
      return homeGoals > awayGoals ? 'W' : 'L';
    } else {
      return awayGoals > homeGoals ? 'W' : 'L';
    }
  };

  // Calculate head-to-head statistics
  const homeTeamId = fixture.teams.home.id;
  
  let homeWins = 0;
  let draws = 0;
  let awayWins = 0;
  let homeGoalsTotal = 0;
  let awayGoalsTotal = 0;

  headToHead.forEach(match => {
    const result = getMatchResult(match, homeTeamId);
    switch (result) {
      case 'W': homeWins++; break;
      case 'D': draws++; break;
      case 'L': awayWins++; break;
    }
    
    // Count goals (adjust based on which team was home/away in historical match)
    if (match.teams.home.id === homeTeamId) {
      homeGoalsTotal += match.goals.home || 0;
      awayGoalsTotal += match.goals.away || 0;
    } else {
      homeGoalsTotal += match.goals.away || 0;
      awayGoalsTotal += match.goals.home || 0;
    }
  });

  const totalMatches = headToHead.length;

  return (
    <div className="bg-card rounded-lg border border-border p-6">
      <h3 className="text-lg font-semibold mb-6">Head-to-Head</h3>
      
      {/* Overall Statistics */}
      <div className="mb-8">
        <h4 className="font-medium mb-4">Overall Record ({totalMatches} matches)</h4>
        
        <div className="grid grid-cols-3 gap-4 text-center mb-6">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{homeWins}</div>
            <div className="text-sm text-muted-foreground">{fixture.teams.home.name} wins</div>
          </div>
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-gray-600">{draws}</div>
            <div className="text-sm text-muted-foreground">Draws</div>
          </div>
          <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{awayWins}</div>
            <div className="text-sm text-muted-foreground">{fixture.teams.away.name} wins</div>
          </div>
        </div>

        {/* Goals Statistics */}
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="p-3 bg-muted rounded-lg">
            <div className="text-lg font-semibold">{homeGoalsTotal}</div>
            <div className="text-sm text-muted-foreground">Goals scored</div>
            <div className="text-xs text-muted-foreground">{fixture.teams.home.name}</div>
          </div>
          <div className="p-3 bg-muted rounded-lg">
            <div className="text-lg font-semibold">{awayGoalsTotal}</div>
            <div className="text-sm text-muted-foreground">Goals scored</div>
            <div className="text-xs text-muted-foreground">{fixture.teams.away.name}</div>
          </div>
        </div>
      </div>

      {/* Recent Matches */}
      <div>
        <h4 className="font-medium mb-4">Recent Matches</h4>
        <div className="space-y-3">
          {headToHead.slice(0, 10).map((match, index) => {
            const homeResult = getMatchResult(match, homeTeamId);
            const isHomeTeamHome = match.teams.home.id === homeTeamId;
            
            return (
              <div key={match.fixture?.id || index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="text-xs text-muted-foreground">
                    {new Date(match.fixture?.date || '').toLocaleDateString()}
                  </div>
                  <div className="text-sm">
                    {isHomeTeamHome ? (
                      <>
                        <span className="font-medium">{fixture.teams.home.name}</span>
                        <span className="mx-2 text-muted-foreground">vs</span>
                        <span>{fixture.teams.away.name}</span>
                      </>
                    ) : (
                      <>
                        <span className="font-medium">{fixture.teams.away.name}</span>
                        <span className="mx-2 text-muted-foreground">vs</span>
                        <span>{fixture.teams.home.name}</span>
                      </>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="text-sm font-medium">
                    {isHomeTeamHome ? 
                      `${match.goals.home}-${match.goals.away}` : 
                      `${match.goals.away}-${match.goals.home}`
                    }
                  </div>
                  <div className={`w-6 h-6 rounded text-xs font-bold flex items-center justify-center ${
                    homeResult === 'W' ? 'bg-green-500 text-white' :
                    homeResult === 'D' ? 'bg-yellow-500 text-white' :
                    'bg-red-500 text-white'
                  }`}>
                    {homeResult}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
