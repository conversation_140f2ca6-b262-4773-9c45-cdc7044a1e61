'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Fixture } from '@/lib/api';
import { FixturePredictions } from '@/types/fixture';

interface FixtureSidebarProps {
  fixture: Fixture;
  predictions?: FixturePredictions;
}

export default function FixtureSidebar({ fixture }: FixtureSidebarProps) {
  const [selectedWinner, setSelectedWinner] = useState<'home' | 'draw' | 'away' | null>(null);



  return (
    <div className="space-y-6">
      {/* Who will win? Section */}
      <div className="bg-card rounded-lg border border-border p-4">
        <h3 className="font-semibold text-sm mb-4">Who will win?</h3>
        <div className="flex space-x-2">
          {/* Home Team Button */}
          <button
            onClick={() => setSelectedWinner(selectedWinner === 'home' ? null : 'home')}
            className={`flex-1 flex items-center justify-center p-3 rounded-lg border transition-all ${
              selectedWinner === 'home'
                ? 'bg-primary text-primary-foreground border-primary'
                : 'bg-muted hover:bg-muted/80 border-border'
            }`}
          >
            <Image
              src={fixture.teams.home.logo}
              alt={fixture.teams.home.name}
              width={24}
              height={24}
              className="w-6 h-6"
            />
          </button>

          {/* Draw Button */}
          <button
            onClick={() => setSelectedWinner(selectedWinner === 'draw' ? null : 'draw')}
            className={`flex-1 flex items-center justify-center p-3 rounded-lg border transition-all ${
              selectedWinner === 'draw'
                ? 'bg-primary text-primary-foreground border-primary'
                : 'bg-muted hover:bg-muted/80 border-border'
            }`}
          >
            <div className="w-6 h-6 flex items-center justify-center text-lg font-bold">
              X
            </div>
          </button>

          {/* Away Team Button */}
          <button
            onClick={() => setSelectedWinner(selectedWinner === 'away' ? null : 'away')}
            className={`flex-1 flex items-center justify-center p-3 rounded-lg border transition-all ${
              selectedWinner === 'away'
                ? 'bg-primary text-primary-foreground border-primary'
                : 'bg-muted hover:bg-muted/80 border-border'
            }`}
          >
            <Image
              src={fixture.teams.away.logo}
              alt={fixture.teams.away.name}
              width={24}
              height={24}
              className="w-6 h-6"
            />
          </button>
        </div>
      </div>
    </div>
  );
}
