'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { ChevronDown, ChevronRight, Search } from 'lucide-react';
import { apiService, League, handleApiError } from '@/lib/api';

interface CountryGroup {
  name: string;
  flag: string | null;
  leagues: League[];
}

interface MobileLeaguesViewProps {
  onLeagueSelect?: (leagueId: number) => void;
  selectedLeagueId?: number | null;
}

export default function MobileLeaguesView({ onLeagueSelect, selectedLeagueId }: MobileLeaguesViewProps) {
  const [allLeaguesOpen, setAllLeaguesOpen] = useState(false);
  const [topLeagues, setTopLeagues] = useState<League[]>([]);
  const [allLeagues, setAllLeagues] = useState<League[]>([]);
  const [isLoadingTopLeagues, setIsLoadingTopLeagues] = useState(true);
  const [isLoadingAllLeagues, setIsLoadingAllLeagues] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedCountries, setExpandedCountries] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');

  // Function to format league names by removing UEFA prefix
  const formatLeagueName = (name: string): string => {
    if (name === 'UEFA Champions League') return 'Champions League';
    if (name === 'UEFA Europa League') return 'Europa League';
    if (name === 'UEFA Europa Conference League') return 'Conference League';
    return name;
  };

  useEffect(() => {
    const fetchLeagues = async () => {
      setError(null);
      setIsLoadingTopLeagues(true);
      try {
        const leaguesData = await apiService.getLeagues();
        const topLeagueIds = [2, 3, 39, 140, 78, 135, 61, 848, 40, 45];
        const topLeaguesFiltered = leaguesData.filter(l => topLeagueIds.includes(l._id));
        const leagueOrder = [2, 3, 39, 140, 78, 135, 61, 848, 40, 45];
        const sortedTopLeagues = topLeaguesFiltered.sort((a, b) => {
          return leagueOrder.indexOf(a._id) - leagueOrder.indexOf(b._id);
        });
        setTopLeagues(sortedTopLeagues);
        setAllLeagues(leaguesData);
      } catch (error) {
        console.error('Error fetching leagues:', error);
        setError(handleApiError(error));
      } finally {
        setIsLoadingTopLeagues(false);
      }
    };

    fetchLeagues();
  }, []);

  const groupLeaguesByCountry = (leaguesList: League[]): CountryGroup[] => {
    const map = new Map<string, CountryGroup>();
    leaguesList.forEach(league => {
      const name = league.country.name;
      if (!map.has(name)) {
        map.set(name, {
          name,
          flag: league.country.flag,
          leagues: [],
        });
      }
      map.get(name)!.leagues.push(league);
    });
    return Array.from(map.values())
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(group => ({
        ...group,
        leagues: group.leagues.sort((a, b) => a.league.name.localeCompare(b.league.name)),
      }));
  };

  const filteredAllLeagues = allLeagues.filter(l =>
    l.league.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    l.country.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const countryGroups = groupLeaguesByCountry(filteredAllLeagues);

  const toggleCountry = (name: string) => {
    const updated = new Set(expandedCountries);
    if (updated.has(name)) {
      updated.delete(name);
    } else {
      updated.add(name);
    }
    setExpandedCountries(updated);
  };

  const LeagueItemSkeleton = () => (
    <div className="flex items-center space-x-3 p-2 w-full rounded-lg animate-pulse">
      <div className="w-[18px] h-[18px] bg-muted rounded flex-shrink-0" />
      <div className="flex-1 min-w-0">
        <div className="h-4 bg-muted rounded w-3/4" />
      </div>
    </div>
  );

  const CountryItemSkeleton = () => (
    <div className="rounded-lg overflow-hidden w-full animate-pulse">
      <div className="flex items-center justify-between p-3">
        <div className="flex items-center space-x-3">
          <div className="w-[18px] h-[18px] bg-muted rounded-full flex-shrink-0" />
          <div className="h-4 bg-muted rounded w-24" />
        </div>
        <div className="w-4 h-4 bg-muted rounded" />
      </div>
    </div>
  );

  const LeagueItem = ({ league }: { league: League }) => (
    <button
      onClick={() => onLeagueSelect?.(league._id)}
      className={`flex items-center space-x-3 p-2 w-full hover:bg-muted rounded-lg transition-colors ${
        selectedLeagueId === league._id ? 'bg-muted' : ''
      }`}
    >
      <div className="w-[18px] h-[18px] bg-white rounded flex items-center justify-center overflow-hidden flex-shrink-0">
        <Image
          src={league.league.logo}
          alt={formatLeagueName(league.league.name)}
          width={18}
          height={18}
          className="w-[18px] h-[18px] object-contain"
          loading="lazy"
          onError={(e) => {
            const parent = e.currentTarget.parentElement;
            if (parent) {
              const formattedName = formatLeagueName(league.league.name);
              parent.innerHTML =
                '<span class="text-xs font-bold text-muted-foreground">' +
                formattedName.charAt(0) +
                '</span>';
            }
          }}
        />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate text-foreground text-left">
          {formatLeagueName(league.league.name)}
        </p>
      </div>
    </button>
  );

  return (
    <div className="h-full overflow-y-auto pb-20 bg-background">
      <div className="p-4 space-y-4">
        {/* Top Leagues */}
        <div className="bg-card rounded-lg border border-border overflow-hidden">
          <div className="p-4">
            <h3 className="font-semibold text-foreground mb-4" style={{ fontSize: '16px' }}>
              Top Leagues
            </h3>

            <div className="space-y-1">
              {isLoadingTopLeagues ? (
                Array.from({ length: 10 }).map((_, i) => <LeagueItemSkeleton key={i} />)
              ) : topLeagues.length > 0 ? (
                topLeagues.map(league => <LeagueItem key={league._id} league={league} />)
              ) : error ? (
                <div className="text-destructive text-sm py-2">Failed to load leagues</div>
              ) : (
                <div className="text-muted-foreground text-sm py-2">No leagues available</div>
              )}
            </div>
          </div>
        </div>

        {/* All Leagues */}
        <div className="bg-card rounded-lg border border-border overflow-hidden">
          <button
            onClick={() => {
              if (!allLeaguesOpen && allLeagues.length === 0 && !isLoadingAllLeagues) {
                setIsLoadingAllLeagues(true);
                setTimeout(() => setIsLoadingAllLeagues(false), 800);
              }
              setAllLeaguesOpen(!allLeaguesOpen);
            }}
            className="w-full flex items-center justify-between p-4 hover:bg-muted transition-colors"
          >
            <h3 className="font-semibold text-foreground" style={{ fontSize: '16px' }}>All leagues</h3>
            {allLeaguesOpen
              ? <ChevronDown className="w-5 h-5 text-muted-foreground" />
              : <ChevronRight className="w-5 h-5 text-muted-foreground" />}
          </button>

          {allLeaguesOpen && (
            <div className="px-4 pb-4">
              {/* Search */}
              <div className="flex items-center bg-muted rounded-lg px-3 py-2 mb-4">
                <Search className="w-4 h-4 text-muted-foreground mr-2" />
                <input
                  type="text"
                  placeholder="Search leagues..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground"
                />
              </div>

              <div className="space-y-2">
                {isLoadingAllLeagues
                  ? Array.from({ length: 3 }).map((_, i) => <CountryItemSkeleton key={i} />)
                  : countryGroups.length > 0
                  ? countryGroups.map(country => (
                      <div key={country.name} className="rounded-lg overflow-hidden w-full">
                        <button
                          onClick={() => toggleCountry(country.name)}
                          className="w-full flex items-center justify-between p-3 hover:bg-muted transition-colors"
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-[18px] h-[18px] flex items-center justify-center flex-shrink-0">
                              {country.flag ? (
                                <Image
                                  src={country.flag}
                                  alt={country.name}
                                  width={18}
                                  height={18}
                                  className="w-[18px] h-[18px] object-cover rounded-full"
                                  loading="lazy"
                                  onError={(e) => {
                                    const parent = e.currentTarget.parentElement;
                                    if (parent) {
                                      parent.innerHTML =
                                        '<span class="text-xs font-bold text-muted-foreground">' +
                                        country.name.charAt(0) +
                                        '</span>';
                                    }
                                  }}
                                />
                              ) : (
                                <span className="text-xs font-bold text-muted-foreground">
                                  {country.name.charAt(0)}
                                </span>
                              )}
                            </div>
                            <span className="text-sm font-medium text-foreground">{country.name}</span>
                          </div>
                          <ChevronDown
                            className={`w-4 h-4 text-muted-foreground transition-transform ${
                              expandedCountries.has(country.name) ? 'rotate-180' : ''
                            }`}
                          />
                        </button>
                        {expandedCountries.has(country.name) && (
                          <div className="px-3 pb-3 space-y-1">
                            {country.leagues.map(league => (
                              <LeagueItem key={league._id} league={league} />
                            ))}
                          </div>
                        )}
                      </div>
                    ))
                  : <div className="text-muted-foreground text-sm py-2">
                      {searchQuery ? `No leagues found matching "${searchQuery}"` : 'No additional leagues available'}
                    </div>}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
