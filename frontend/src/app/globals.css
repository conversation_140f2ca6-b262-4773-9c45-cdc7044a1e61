@import "tailwindcss";

:root {
--background: #FAFAFA;
--foreground: #171717;
--card: #FFFFFF;
--card-foreground: #171717;
--primary: #00c758;
--primary-foreground: #FFFFFF;
--secondary: #f3f4f6;
--secondary-foreground: #374151;
--muted: rgb(246 245 245);
--muted-foreground: #6b7280;
--accent: #f3f4f6;
--accent-foreground: #374151;
--destructive: #ef4444;
--destructive-foreground: #FFFFFF;
--border: rgba(240, 240, 240, 1.0);
--input: #e5e7eb;
--ring: #00c758;
--container-2x: 45rem;
--radius-lg: 1rem;
--pitch-color: #17AA61;
}

.dark {
--background: #0a0a0a;
--foreground: #ededed;
--card: #1d1d1d;
--card-foreground: #ededed;
--primary: #00c758;
--primary-foreground: #FFFFFF;
--secondary: #262626;
--secondary-foreground: #d4d4d8;
--muted: #262626;
--muted-foreground: #a1a1aa;
--accent: #262626;
--accent-foreground: #d4d4d8;
--destructive: #ef4444;
--destructive-foreground: #FFFFFF;
--border: rgba(64, 64, 64, 0.5);
--input: #404040;
--ring: #00c758;
--container-2x: 45rem;
--radius-lg: 1rem;
--pitch-color: #'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { TeamLineup, MatchEvents, Player } from '@/types/fixture';
import { Fixture } from '@/lib/api';

interface FootballPitchProps {
  homeLineup: TeamLineup;
  awayLineup: TeamLineup;
  fixture: Fixture;
  events?: MatchEvents;
}

export default function FootballPitch({
  homeLineup,
  awayLineup,
  fixture,
  events
}: FootballPitchProps) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Get player goals from events
  const getPlayerGoals = (playerId: number, teamId: number): number => {
    if (!events) return 0;
    
    return events.filter(event => 
      event.player?.id === playerId && 
      event.team.id === teamId && 
      event.type === 'Goal' &&
      !event.detail.toLowerCase().includes('own')
    ).length;
  };

  // Get player cards from events
  const getPlayerCards = (playerId: number, teamId: number): { yellow: number; red: number } => {
    if (!events) return { yellow: 0, red: 0 };
    
    const cardEvents = events.filter(event => 
      event.player?.id === playerId && 
      event.team.id === teamId && 
      event.type === 'Card'
    );
    
    const yellow = cardEvents.filter(event => 
      event.detail.toLowerCase().includes('yellow')
    ).length;
    
    const red = cardEvents.filter(event => 
      event.detail.toLowerCase().includes('red')
    ).length;
    
    return { yellow, red };
  };

  const convertGridToPosition = (
    apiGrid: string,
    isHome: boolean,
    lineup: { player: Player }[],
    isMobile: boolean = false
  ) => {
    const [apiColumn, apiRow] = apiGrid.split(':').map(Number);

    const totalPlayersInLine = lineup.filter(p =>
      p.player.grid?.startsWith(`${apiColumn}:`)
    ).length;

    if (isMobile) {
      // Vertical layout for mobile
      const baseXPositionPercentage = (apiRow - 0.5) / totalPlayersInLine * 100;
      const xPositionPercentage = isHome ? baseXPositionPercentage : (100 - baseXPositionPercentage);

      // Home team (bottom) is shifted down, Away team (top) is shifted up
      const homeColumnPositions = {
        1: 95, // GK
        2: 82, // Defenders
        3: 71, // Defensive Midfielders
        4: 61, // Attacking Midfielders
        5: 55, // Strikers
      };
      const awayColumnPositions = {
        1: 5,  // GK
        2: 18, // Defenders
        3: 29, // Defensive Midfielders
        4: 39, // Attacking Midfielders
        5: 45, // Strikers
      };
      const columnPositions = isHome ? homeColumnPositions : awayColumnPositions;
      const yPositionPercentage = columnPositions[apiColumn as keyof typeof columnPositions] || 50;

      return { x: xPositionPercentage, y: yPositionPercentage };
    } else {
      // Horizontal layout for desktop
      const yPositionPercentage = (apiRow - 0.5) / totalPlayersInLine * 100;

      // Home team (left) is shifted left, Away team (right) is shifted right
      const homeColumnPositions = {
        1: 5,  // GK
        2: 18, // Defenders
        3: 29, // Defensive Midfielders
        4: 39, // Attacking Midfielders
        5: 45, // Strikers
      };
      const awayColumnPositions = {
        1: 95, // GK
        2: 82, // Defenders
        3: 71, // Defensive Midfielders
        4: 61, // Attacking Midfielders
        5: 55, // Strikers
      };
      const columnPositions = isHome ? homeColumnPositions : awayColumnPositions;
      const xPositionPercentage = columnPositions[apiColumn as keyof typeof columnPositions] || 50;

      return { x: xPositionPercentage, y: yPositionPercentage };
    }
  };

  // Render a player
  const renderPlayer = (player: Player, isHome: boolean) => {
    const goals = getPlayerGoals(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);
    const cards = getPlayerCards(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);

    const homeColors = homeLineup.team.colors?.player;
    const awayColors = awayLineup.team.colors?.player;
    const homeGkColors = homeLineup.team.colors?.goalkeeper;
    const awayGkColors = awayLineup.team.colors?.goalkeeper;

    let playerColor: string;
    let textColor: string;
    
    if (player.pos === 'G') {
      playerColor = isHome ? `#${homeGkColors?.primary || '059669'}` : `#${awayGkColors?.primary || 'dc2626'}`;
      textColor = isHome ? `#${homeGkColors?.number || 'ffffff'}` : `#${awayGkColors?.number || 'ffffff'}`;
    } else {
      playerColor = isHome ? `#${homeColors?.primary || '3b82f6'}` : `#${awayColors?.primary || 'ef4444'}`;
      textColor = isHome ? `#${homeColors?.number || 'ffffff'}` : `#${awayColors?.number || 'ffffff'}`;
    }

    return (
      <div key={player.id} className="flex flex-col items-center">
        {/* Player Jersey Container */}
        <div className="relative w-10 h-10">
          {/* SVG Jersey Shape */}
          <svg
            viewBox="0 0 36 36"
            className="w-full h-full drop-shadow-lg"
          >
            <path
              fill={playerColor}
              stroke="#FFFFFF"
              strokeWidth="1.5"
              d="M 6 5 L 12 5 C 14 3, 22 3, 24 5 L 30 5 L 34 10 L 30 14 L 30 31 L 6 31 L 6 14 L 2 10 L 6 5 Z"
            />
          </svg>

          {/* Player Number */}
          <div
            className="absolute inset-0 flex items-center justify-center font-bold text-sm"
            style={{ color: textColor }}
          >
            <span>{player.number || '?'}</span>
          </div>

          {/* Goal indicator */}
          {goals > 0 && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center text-xs text-white font-bold">
              {goals}
            </div>
          )}

          {/* Card indicators (Prioritizes Red over Yellow) */}
          {cards.red > 0 ? (
            <div className="absolute -top-1.5 -left-1.5 w-3 h-4 bg-red-500 rounded-sm"></div>
          ) : cards.yellow > 0 && (
            <div className="absolute -top-1.5 -left-1.5 w-3 h-4 bg-yellow-400 rounded-sm"></div>
          )}
        </div>

        {/* Player Name */}
        <div 
          className="mt-1 text-xs text-center text-white font-medium max-w-16 truncate"
          style={{ textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)' }}
        >
          {player.name.split(' ').pop()}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-card rounded-lg border border-border p-4 md:p-6">
      {/* Team Headers */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-3">
          <Image
            src={homeLineup.team.logo}
            alt={homeLineup.team.name}
            width={32}
            height={32}
            className="w-8 h-8"
          />
          <div>
            <h4 className="font-semibold">{homeLineup.team.name}</h4>
            {homeLineup.formation && (
              <p className="text-sm text-muted-foreground">{homeLineup.formation}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div>
            <h4 className="font-semibold text-right">{awayLineup.team.name}</h4>
            {awayLineup.formation && (
              <p className="text-sm text-muted-foreground text-right">{awayLineup.formation}</p>
            )}
          </div>
          <Image
            src={awayLineup.team.logo}
            alt={awayLineup.team.name}
            width={32}
            height={32}
            className="w-8 h-8"
          />
        </div>
      </div>

      {/* Football Pitch */}
      <div className="w-full max-w-6xl mx-auto">
        <div
          className="relative w-full shadow-lg overflow-hidden"
          style={{
            paddingBottom: isMobile ? '150%' : '60%',
            backgroundColor: 'var(--pitch-color, #17AA61)'
          }}
        >
          {/* Pitch Markings */}
          <svg className="absolute inset-0 w-full h-full">
            {/* Outer boundary */}
            <rect x="2" y="2" width="calc(100% - 4px)" height="calc(100% - 4px)" fill="none" stroke="white" strokeWidth="2"/>

            {/* Center line and circle - responsive */}
            <g className="hidden md:block">
              {/* Horizontal layout for desktop */}
              <line x1="50%" y1="0" x2="50%" y2="100%" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="50%" r="12%" fill="none" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="50%" r="0.5%" fill="white"/>
              <rect x="0" y="35%" width="8%" height="30%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="92%" y="35%" width="8%" height="30%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="0" y="25%" width="15%" height="50%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="85%" y="25%" width="15%" height="50%" fill="none" stroke="white" strokeWidth="2"/>
              <circle cx="10%" cy="50%" r="0.5%" fill="white"/>
              <circle cx="90%" cy="50%" r="0.5%" fill="white"/>
            </g>

            <g className="block md:hidden">
              {/* Vertical layout for mobile */}
              <line x1="0" y1="50%" x2="100%" y2="50%" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="50%" r="8%" fill="none" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="50%" r="0.5%" fill="white"/>
              <rect x="35%" y="0" width="30%" height="8%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="35%" y="92%" width="30%" height="8%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="25%" y="0" width="50%" height="15%" fill="none" stroke="white" strokeWidth="2"/>
              <rect x="25%" y="85%" width="50%" height="15%" fill="none" stroke="white" strokeWidth="2"/>
              <circle cx="50%" cy="10%" r="0.5%" fill="white"/>
              <circle cx="50%" cy="90%" r="0.5%" fill="white"/>
            </g>
          </svg>

          {/* Players Positioned Using Grid System */}
          <div className="absolute inset-0">
            {homeLineup.startXI?.map((playerData) => {
              const player = playerData.player;
              if (!player.grid || !homeLineup.startXI) return null;
              const position = convertGridToPosition(player.grid, true, homeLineup.startXI, isMobile);
              return (
                <div
                  key={player.id}
                  className="absolute"
                  style={{
                    left: `${position.x}%`,
                    top: `${position.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  {renderPlayer(player, true)}
                </div>
              );
            })}

            {awayLineup.startXI?.map((playerData) => {
              const player = playerData.player;
              if (!player.grid || !awayLineup.startXI) return null;
              const position = convertGridToPosition(player.grid, false, awayLineup.startXI, isMobile);
              return (
                <div
                  key={player.id}
                  className="absolute"
                  style={{
                    left: `${position.x}%`,
                    top: `${position.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  {renderPlayer(player, false)}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
}

@theme inline {
--color-background: var(--background);
--color-foreground: var(--foreground);
--color-card: var(--card);
--color-card-foreground: var(--card-foreground);
--color-primary: var(--primary);
--color-primary-foreground: var(--primary-foreground);
--color-secondary: var(--secondary);
--color-secondary-foreground: var(--secondary-foreground);
--color-muted: var(--muted);
--color-muted-foreground: var(--muted-foreground);
--color-accent: var(--accent);
--color-accent-foreground: var(--accent-foreground);
--color-destructive: var(--destructive);
--color-destructive-foreground: var(--destructive-foreground);
--color-border: var(--border);
--color-input: var(--input);
--color-ring: var(--ring);
--font-sans: 'Inter', 'Inter var', system-ui, -apple-system, sans-serif;
--container-2x: var(--container-2x);
--radius-lg: var(--radius-lg);
}

body {
background: var(--background);
color: var(--foreground);
font-family: 'Inter', 'Inter var', system-ui, -apple-system, sans-serif;
font-feature-settings: 'liga' 1, 'calt' 1; /* Enable ligatures and contextual alternates */
}

/* Support for Inter variable font */
@supports (font-variation-settings: normal) {
body {
font-family: 'Inter var', 'Inter', system-ui, -apple-system, sans-serif;
}
}

/* CLS Prevention - Ensure images maintain layout */
img {
/* Prevent layout shifts during image loading */
display: block;
max-width: 100%;
height: 100%;
}

/* Aspect ratio containers for consistent image sizing */
.aspect-square {
aspect-ratio: 1 / 1;
}

.aspect-flag {
aspect-ratio: 3 / 2;
}

/* Prevent layout shifts from font loading */
.font-display-swap {
font-display: swap;
}


/* Override Tailwind's transition classes to remove unwanted properties */
.transition-all {
transition-property: none !important;
transition-timing-function: none !important;
transition-duration: 0s !important;
}

.transition-colors {
transition-property: none !important;
transition-timing-function: none !important;
transition-duration: 0s !important;
}

/* Skeleton loading improvements */
.skeleton {
background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
background-size: 200% 100%;
animation: loading 1.5s infinite;
}

@keyframes loading {
0% {
background-position: -200% 0;
}
100% {
background-position: 200% 0;
}
}

/* Mobile Bottom Navigation Styles - Floating Design */
.mobile-bottom-nav-floating {
  position: fixed;
  bottom: 16px;
  left: 16px;
  right: 16px;
  z-index: 50;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 20px;
  padding: 8px 16px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 8px 20px -4px rgba(0, 0, 0, 0.1),
    0 4px 8px -4px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  width: auto;
  height: 56px;
}

/* Dark theme enhancements */
.dark .mobile-bottom-nav-floating {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 8px 10px -6px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

@media (min-width: 768px) {
  .mobile-bottom-nav-floating {
    display: none;
  }
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  position: relative;
  flex: 1;
  border-radius: 12px;
  height: 40px;
}

/* Only show underline for active items */
.mobile-nav-item::before {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0px;
  height: 2px;
  background: var(--accent-color, #1A3050);
  border-radius: 1px;
}

.mobile-nav-item.active::before {
  width: var(--lineWidth, 0px);
}

/* Light theme active state */
.mobile-nav-item.active {
  background: rgba(26, 48, 80, 0.08);
}

/* Dark theme styles */
.dark .mobile-nav-item::before {
  background: #00c758;
}

.dark .mobile-nav-item.active {
  background: rgba(0, 199, 88, 0.1);
}

.mobile-nav-icon {
  margin-bottom: 2px;
}

.mobile-nav-item.active .mobile-nav-icon {
  animation: iconBounce 0.6s ease;
}

.mobile-nav-icon .icon {
  width: 18px;
  height: 18px;
  color: var(--muted-foreground);
}

/* Light theme active icon */
.mobile-nav-item.active .mobile-nav-icon .icon {
  color: var(--accent-color, #1A3050);
}

/* Dark theme icon colors */
.dark .mobile-nav-icon .icon {
  color: #ffffff;
}

.dark .mobile-nav-item.active .mobile-nav-icon .icon {
  color: #00c758;
}

.mobile-nav-text {
  font-size: 10px;
  font-weight: 500;
  color: var(--muted-foreground);
  white-space: nowrap;
  margin-top: 1px;
}

/* Light theme active text */
.mobile-nav-text.active {
  color: var(--accent-color, #1A3050);
  font-weight: 600;
}

/* Dark theme text colors */
.dark .mobile-nav-text {
  color: #ffffff;
}

.dark .mobile-nav-text.active {
  color: #00c758;
  font-weight: 600;
}

@keyframes iconBounce {
  0%, 100% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-3px);
  }
  40% {
    transform: translateY(0);
  }
  60% {
    transform: translateY(-1px);
  }
  80% {
    transform: translateY(0);
  }
}

/* Hide scrollbars */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Add bottom padding on mobile to account for floating bottom navigation */
@media (max-width: 767px) {
  body {
    padding-bottom: 80px;
  }
}