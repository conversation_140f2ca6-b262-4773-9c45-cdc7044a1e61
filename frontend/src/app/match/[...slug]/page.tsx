'use client';

import { useState, useEffect } from 'react';
import { useParams, notFound } from 'next/navigation';
import { useLiveFixtures } from '@/hooks/useSocket';
import FixtureHeader from '@/components/fixture/FixtureHeader';
import FixtureContent from '@/components/fixture/FixtureContent';
import FixtureSidebar from '@/components/fixture/FixtureSidebar';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import {
  FixturePageData,
  MatchStatistics,
  MatchEvents,
  MatchLineups,
  HeadToHeadData,
  FixturePredictions
} from '@/types/fixture';

export default function FixturePage() {
  const params = useParams();
  const [fixtureData, setFixtureData] = useState<FixturePageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('facts');

  // WebSocket for live updates
  const { liveFixtures, connected } = useLiveFixtures();

  // Debug WebSocket connection
  useEffect(() => {
    console.log('WebSocket connected:', connected);
    console.log('Live fixtures count:', liveFixtures.length);
    if (liveFixtures.length > 0) {
      console.log('Live fixtures:', liveFixtures.map(f => ({ id: f._id, status: f.fixture.status.short, teams: `${f.teams.home.name} vs ${f.teams.away.name}` })));
    }
  }, [connected, liveFixtures]);

  // Parse URL slug to extract fixture ID
  const parseSlug = (slug: string[]): { fixtureId: number; teamSlug: string } | null => {
    if (!slug || slug.length < 2) return null;
    
    // Last segment should be the fixture ID
    const fixtureId = parseInt(slug[slug.length - 1]);
    if (isNaN(fixtureId)) return null;
    
    // Everything before the last segment is the team slug
    const teamSlug = slug.slice(0, -1).join('/');
    
    return { fixtureId, teamSlug };
  };



  useEffect(() => {
    const fetchFixtureData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Parse the URL slug
        const slugArray = Array.isArray(params.slug) ? params.slug : [params.slug].filter((s): s is string => Boolean(s));
        const parsedSlug = parseSlug(slugArray);
        
        if (!parsedSlug) {
          notFound();
        }

        const { fixtureId } = parsedSlug;

        // Fetch main fixture data
        const fixtureResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures?id=${fixtureId}`, {
          headers: {
            'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
          }
        });

        if (!fixtureResponse.ok) {
          if (fixtureResponse.status === 404) {
            notFound();
          }
          throw new Error('Failed to fetch fixture data');
        }

        const fixtures = await fixtureResponse.json();
        const fixture = Array.isArray(fixtures) ? fixtures[0] : fixtures;

        if (!fixture) {
          notFound();
        }

        // Validate URL team slug matches actual teams (optional - for SEO consistency)
        // const expectedSlug = generateTeamSlug(fixture);
        // Note: We could redirect to correct URL here if slug doesn't match, but for now we'll allow it

        // Fetch additional data based on match status
        const dataPromises: Promise<unknown>[] = [];
        
        // Always try to fetch these (they may or may not be available)
        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/statistics`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/events`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/lineups`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        // Head-to-head data
        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/h2h?h2h=${fixture.teams.home.id}-${fixture.teams.away.id}`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        // Predictions data
        dataPromises.push(
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/predictions?fixture=${fixtureId}`, {
            headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
          }).then(res => res.ok ? res.json() : null).catch(() => null)
        );

        const [statistics, events, lineups, headToHead, predictions] = await Promise.all(dataPromises);

        setFixtureData({
          fixture,
          statistics: statistics as MatchStatistics | undefined,
          events: events as MatchEvents | undefined,
          lineups: lineups as MatchLineups | undefined,
          headToHead: headToHead as HeadToHeadData | undefined,
          predictions: predictions as FixturePredictions | undefined
        });

        // Set default active tab based on match status
        if (fixture.fixture.status.short === 'NS') {
          setActiveTab('preview');
        } else {
          setActiveTab('facts');
        }

      } catch (err) {
        console.error('Error fetching fixture data:', err);
        setError('Failed to load fixture data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchFixtureData();
  }, [params.slug]);

  // Handle live fixture updates via WebSocket (same as homepage)
  useEffect(() => {
    if (liveFixtures.length > 0 && fixtureData) {
      console.log('Checking for live updates...');
      console.log('Current fixture ID:', fixtureData.fixture._id);
      console.log('Current fixture API ID:', fixtureData.fixture.fixture.id);
      console.log('Live fixture IDs:', liveFixtures.map(f => ({ _id: f._id, apiId: f.fixture.id })));

      // Find if our current fixture is in the live updates
      const liveFixture = liveFixtures.find(f =>
        f._id === fixtureData.fixture._id ||
        f.fixture.id === fixtureData.fixture.fixture.id ||
        f._id === fixtureData.fixture.fixture.id || // Sometimes _id matches API ID
        f.fixture.id === fixtureData.fixture._id // Sometimes API ID matches _id
      );

      if (liveFixture) {
        console.log('✅ Live fixture update received:', liveFixture);

        // Check if there are significant changes (goals, status, time)
        const hasGoalUpdate = liveFixture.goals.home !== fixtureData.fixture.goals.home ||
                             liveFixture.goals.away !== fixtureData.fixture.goals.away;
        const hasStatusUpdate = liveFixture.fixture.status.short !== fixtureData.fixture.fixture.status.short ||
                               liveFixture.fixture.status.elapsed !== fixtureData.fixture.fixture.status.elapsed;

        // Update the fixture data with live data
        setFixtureData(prev => prev ? { ...prev, fixture: liveFixture } : null);

        // If there are significant updates, refetch events and statistics
        if (hasGoalUpdate || hasStatusUpdate) {
          console.log('Significant update detected, refetching events and statistics...');
          refetchLiveData(liveFixture.fixture.id);
        }
      }
    }
  }, [liveFixtures, fixtureData]);

  // Function to refetch live data (events, statistics)
  const refetchLiveData = async (fixtureId: number) => {
    try {
      // Fetch updated events
      const eventsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/events`, {
        headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
      });

      if (eventsResponse.ok) {
        const newEvents = await eventsResponse.json();
        setFixtureData(prev => prev ? { ...prev, events: newEvents } : null);
      }

      // Fetch updated statistics
      const statsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/fixtures/${fixtureId}/statistics`, {
        headers: { 'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756' }
      });

      if (statsResponse.ok) {
        const newStats = await statsResponse.json();
        setFixtureData(prev => prev ? { ...prev, statistics: newStats } : null);
      }
    } catch (error) {
      console.error('Error refetching live data:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !fixtureData) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Error Loading Match</h1>
            <p className="text-muted-foreground mb-4">{error || 'Match not found'}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90"
            >
              Try Again
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />



      <div className="max-w-7xl mx-auto px-4 py-4 md:py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-4 md:space-y-6">
            {/* Fixture Header with Navigation Tabs */}
            <FixtureHeader
              fixture={fixtureData.fixture}
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Tab Content */}
            <FixtureContent
              activeTab={activeTab}
              fixtureData={fixtureData}
            />
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <FixtureSidebar
              fixture={fixtureData.fixture}
              predictions={fixtureData.predictions}
            />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
